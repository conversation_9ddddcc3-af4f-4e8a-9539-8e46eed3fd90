'use client';

import { useState, useEffect, useCallback } from 'react';
import { HROneIDIntegration, HROneIDUser, HRAuthResult } from '../services/HROneIDIntegration';
import { HRRouter } from '../utils/hrRouter';

export interface UseHRAuthReturn {
  user: HROneIDUser | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<HRAuthResult>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasModuleAccess: (module: string) => boolean;
  checkSessionExpiry: () => boolean;
}

export function useHRAuth(): UseHRAuthReturn {
  const [user, setUser] = useState<HROneIDUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [integration] = useState(() => HROneIDIntegration.getInstance());

  // Initialize authentication on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  // Set up session expiry checking
  useEffect(() => {
    if (user?.sessionExpiry) {
      const checkInterval = setInterval(() => {
        if (!checkSessionExpiry()) {
          handleSessionExpiry();
        }
      }, 60000); // Check every minute

      return () => clearInterval(checkInterval);
    }
  }, [user]);

  const initializeAuth = async () => {
    try {
      setLoading(true);
      const result = await integration.initialize();
      
      if (result.success && result.user) {
        setUser(result.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Failed to initialize HR auth:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (username: string, password: string): Promise<HRAuthResult> => {
    try {
      setLoading(true);
      const result = await integration.authenticateUser(username, password);

      if (result.success && result.user) {
        setUser(result.user);
        
        // Trigger a custom event for cross-app session sync
        window.dispatchEvent(new CustomEvent('hr-auth-login', {
          detail: { user: result.user, token: result.token }
        }));
      }

      return result;
    } catch (error) {
      console.error('HR login failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Login failed' 
      };
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setLoading(true);
      const token = user?.oneIdToken || user?.universalToken;
      
      await integration.logout(token);
      setUser(null);

      // Trigger a custom event for cross-app session sync
      window.dispatchEvent(new CustomEvent('hr-auth-logout'));

      // Redirect to login page
      HRRouter.toLogin();
    } catch (error) {
      console.error('HR logout failed:', error);
      // Still clear user even if server logout fails
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const refreshSession = async (): Promise<void> => {
    if (!user?.oneIdToken && !user?.universalToken) return;

    try {
      const token = user.oneIdToken || user.universalToken!;
      const result = await integration.validateSession(token);
      
      if (result.success && result.user) {
        setUser(result.user);
      } else {
        // Session invalid, logout
        await logout();
      }
    } catch (error) {
      console.error('Session refresh failed:', error);
      await logout();
    }
  };

  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) return false;
    return user.permissions.includes(permission) || user.permissions.includes('hr:admin:access');
  }, [user]);

  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    if (!user) return false;
    return permissions.some(permission => hasPermission(permission));
  }, [user, hasPermission]);

  const hasModuleAccess = useCallback((module: string): boolean => {
    if (!user) return false;
    return integration.hasModuleAccess(user, module);
  }, [user, integration]);

  const checkSessionExpiry = useCallback((): boolean => {
    if (!user?.sessionExpiry) return true;
    
    const expiryTime = new Date(user.sessionExpiry).getTime();
    const currentTime = Date.now();
    
    return currentTime < expiryTime;
  }, [user]);

  const handleSessionExpiry = async () => {
    console.warn('HR session expired, logging out...');
    await logout();
  };

  return {
    user,
    loading,
    isAuthenticated: !!user,
    login,
    logout,
    refreshSession,
    hasPermission,
    hasAnyPermission,
    hasModuleAccess,
    checkSessionExpiry
  };
}

// Hook for protecting HR routes
export function useHRAuthGuard(requiredPermissions?: string[], redirectTo?: string) {
  const auth = useHRAuth();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);

  useEffect(() => {
    if (auth.loading) {
      setHasAccess(null);
      return;
    }

    if (!auth.isAuthenticated) {
      setHasAccess(false);
      if (redirectTo !== false) {
        HRRouter.push(redirectTo || '/hr/login');
      }
      return;
    }

    if (requiredPermissions && requiredPermissions.length > 0) {
      const hasRequiredPermissions = auth.hasAnyPermission(requiredPermissions);
      setHasAccess(hasRequiredPermissions);
      
      if (!hasRequiredPermissions && redirectTo !== false) {
        HRRouter.push(redirectTo || '/hr/access-denied');
      }
    } else {
      setHasAccess(true);
    }
  }, [auth.loading, auth.isAuthenticated, auth.hasAnyPermission, requiredPermissions, redirectTo]);

  return {
    ...auth,
    hasAccess,
    loading: auth.loading || hasAccess === null
  };
}

// Hook for universal session management across HR apps
export function useUniversalHRSession() {
  const auth = useHRAuth();

  useEffect(() => {
    // Listen for auth events from other HR apps
    const handleAuthLogin = (event: CustomEvent) => {
      console.log('Universal HR session login detected:', event.detail);
      // Refresh our session to sync with other apps
      auth.refreshSession();
    };

    const handleAuthLogout = () => {
      console.log('Universal HR session logout detected');
      // Clear our session to sync with other apps
      if (auth.isAuthenticated) {
        auth.logout();
      }
    };

    window.addEventListener('hr-auth-login', handleAuthLogin as EventListener);
    window.addEventListener('hr-auth-logout', handleAuthLogout);

    return () => {
      window.removeEventListener('hr-auth-login', handleAuthLogin as EventListener);
      window.removeEventListener('hr-auth-logout', handleAuthLogout);
    };
  }, [auth]);

  return auth;
}

// Hook for module-specific authentication
export function useHRModuleAuth(moduleName: string, requiredPermissions?: string[]) {
  const auth = useHRAuth();
  const [moduleAccess, setModuleAccess] = useState<boolean | null>(null);

  useEffect(() => {
    if (auth.loading) {
      setModuleAccess(null);
      return;
    }

    if (!auth.isAuthenticated) {
      setModuleAccess(false);
      return;
    }

    // Check module access
    const hasModuleAccess = auth.hasModuleAccess(moduleName);
    
    // Check specific permissions if provided
    const hasPermissions = requiredPermissions 
      ? auth.hasAnyPermission(requiredPermissions)
      : true;

    setModuleAccess(hasModuleAccess && hasPermissions);
  }, [auth.loading, auth.isAuthenticated, auth.hasModuleAccess, auth.hasAnyPermission, moduleName, requiredPermissions]);

  return {
    ...auth,
    moduleAccess,
    loading: auth.loading || moduleAccess === null
  };
}