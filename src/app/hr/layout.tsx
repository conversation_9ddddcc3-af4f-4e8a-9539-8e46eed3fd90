'use client';

import { Metada<PERSON> } from 'next';
import { HRProvider } from './shared/context/HRContext';
import { HRRouterProvider } from './providers/HRRouterProvider';

// Note: Metadata export needs to be in a separate server component
// For now, we'll handle this in the page components

interface HRLayoutProps {
  children: React.ReactNode;
}

export default function HRLayout({ children }: HRLayoutProps) {
  return (
    <HRProvider>
      <HRRouterProvider>
        <div className="min-h-screen bg-gray-50">
          {children}
        </div>
      </HRRouterProvider>
    </HRProvider>
  );
}
