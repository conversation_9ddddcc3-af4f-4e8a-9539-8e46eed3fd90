'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Users,
  Briefcase,
  FileText,
  Scale,
  Brain,
  Building2,
  UserCheck,
  TrendingUp,
  Search,
  Globe,
  Shield,
  Clock,
  Star,
  ArrowRight,
  CheckCircle,
  LogOut
} from 'lucide-react';
import HRSuiteStatus from './components/HRSuiteStatus';
import HRUnifiedLayout from './shared/components/HRUnifiedLayout';
import { useHR, useModule } from './shared/context/HRContext';
import { useHRAuthGuard } from './hooks/useHRAuth';

export default function HRSuitePage() {
  // Use auth guard to protect this page
  const auth = useHRAuthGuard(['hr:access']);
  const { user, logout } = useHR();
  const { setModuleState } = useModule('dashboard');

  // Dashboard stats - ALWAYS declare this hook first
  const [dashboardStats, setDashboardStats] = useState({
    totalEmployees: 245,
    activeRecruitment: 18,
    onboardingInProgress: 12,
    globalHires: 23,
    openDisputes: 3,
    completedProjects: 156
  });

  // Set module state - ALWAYS call this effect
  useEffect(() => {
    if (!auth.loading && auth.hasAccess) {
      setModuleState({
        currentModule: 'dashboard',
        pageTitle: 'HR Suite Dashboard',
        breadcrumbs: []
      });
    }
  }, [setModuleState, auth.loading, auth.hasAccess]);

  // Show loading while authentication is being checked
  if (auth.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading HR Suite...</p>
        </div>
      </div>
    );
  }

  // Don't render if no access (auth guard will handle redirect)
  if (!auth.hasAccess) {
    return null;
  }
  const hrSolutions = [
    {
      id: 'hunter',
      title: 'Hunter - Job Seekers Portal',
      description: 'Vietnam\'s leading job portal for job seekers to find opportunities and connect with employers',
      icon: Search,
      href: '/hr/hunter',
      category: 'Job Seekers',
      features: ['Job Search', 'Company Profiles', 'Application Tracking', 'Career Resources'],
      status: 'Active',
      color: 'blue'
    },
    {
      id: 'upwork',
      title: 'Upwork Clone - Freelancing',
      description: 'Comprehensive freelancing platform for gig-based work and project management',
      icon: Briefcase,
      href: '/hr/hunter/upwork',
      category: 'Freelancing',
      features: ['Gig Marketplace', 'Proposal System', 'Project Management', 'Payment Processing'],
      status: 'Active',
      color: 'green'
    },
    {
      id: 'abnrfp',
      title: 'ABN RFP Platform',
      description: 'Request for Proposal platform for procurement and vendor management',
      icon: FileText,
      href: '/hr/hunter/abnrfp',
      category: 'Procurement',
      features: ['RFP Creation', 'Proposal Management', 'Vendor Database', 'Evaluation Tools'],
      status: 'Active',
      color: 'purple'
    },
    {
      id: 'abnreferee',
      title: 'ABN Referee - Dispute Resolution',
      description: 'Professional dispute resolution and arbitration system',
      icon: Scale,
      href: '/hr/hunter/apps/abnreferee',
      category: 'Legal',
      features: ['Dispute Management', 'Arbitration', 'Evidence Tracking', 'Resolution Enforcement'],
      status: 'Active',
      color: 'red'
    },
    {
      id: 'onboarding-buddy',
      title: 'AI Onboarding Buddy',
      description: 'AI-powered onboarding system for seamless new hire experience',
      icon: Brain,
      href: '/onboarding-buddy',
      category: 'Onboarding',
      features: ['Personalized Journeys', 'AI Assistant', 'Task Automation', 'Progress Tracking'],
      status: 'Active',
      color: 'indigo'
    },
    {
      id: 'headhunter',
      title: 'Headhunter - Recruiter Portal',
      description: 'Professional recruitment platform for headhunters and talent acquisition specialists',
      icon: Users,
      href: '/hr/headhunter',
      category: 'Recruiters',
      features: ['Candidate Management', 'Client Dashboard', 'AI Recruitment', 'Global EOR Services'],
      status: 'Active',
      color: 'teal'
    },
    {
      id: 'workdone-hrm',
      title: 'WorkDone HRM',
      description: 'Comprehensive HR management system for employee lifecycle',
      icon: UserCheck,
      href: '/workdone/hrm',
      category: 'HR Management',
      features: ['Employee Management', 'Payroll', 'Training', 'Performance Tracking'],
      status: 'Active',
      color: 'orange'
    }
  ];

  const categories = [
    { name: 'Job Seekers', count: 1, icon: Search },
    { name: 'Recruiters', count: 1, icon: Users },
    { name: 'Freelancing', count: 1, icon: Briefcase },
    { name: 'Procurement', count: 1, icon: FileText },
    { name: 'Legal', count: 1, icon: Scale },
    { name: 'Onboarding', count: 1, icon: Brain },
    { name: 'HR Management', count: 1, icon: UserCheck }
  ];

  const stats = [
    { label: 'HR Solutions', value: '7+', icon: Building2 },
    { label: 'Active Users', value: '50K+', icon: Users },
    { label: 'Companies Served', value: '1,000+', icon: Building2 },
    { label: 'Success Rate', value: '95%', icon: TrendingUp }
  ];

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, string> = {
      blue: 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100',
      green: 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100',
      purple: 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100',
      red: 'bg-red-50 border-red-200 text-red-700 hover:bg-red-100',
      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-700 hover:bg-indigo-100',
      teal: 'bg-teal-50 border-teal-200 text-teal-700 hover:bg-teal-100',
      orange: 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100'
    };
    return colorMap[color] || colorMap.blue;
  };

  const getIconColorClasses = (color: string) => {
    const colorMap: Record<string, string> = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      purple: 'text-purple-600',
      red: 'text-red-600',
      indigo: 'text-indigo-600',
      teal: 'text-teal-600',
      orange: 'text-orange-600'
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <HRUnifiedLayout 
      title="HR Suite Dashboard"
      subtitle="Comprehensive Human Resources Solutions"
    >
      <div className="min-h-screen bg-gradient-to-b from-slate-50 to-slate-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">HR Suite</h1>
              <p className="text-gray-600 mt-1">Comprehensive Human Resources Solutions</p>
            </div>
            <div className="flex items-center space-x-4">
              {/* User Info */}
              {user && (
                <div className="flex items-center space-x-3 bg-gray-50 rounded-lg px-3 py-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{user.name}</p>
                    <p className="text-gray-500">{user.roles.join(', ')}</p>
                  </div>
                </div>
              )}
              
              {/* System Status */}
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-green-600" />
                <span className="text-sm text-green-600 font-medium">OneID Active</span>
              </div>

              {/* Logout Button */}
              {user && (
                <button
                  onClick={() => logout()}
                  className="flex items-center space-x-2 px-3 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                  title="Sign Out"
                >
                  <LogOut className="w-4 h-4" />
                  <span className="text-sm font-medium">Sign Out</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-4">
              Complete HR Ecosystem
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              From recruitment to onboarding, from freelancing to dispute resolution - 
              discover our comprehensive suite of HR solutions designed to streamline your workforce management.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="#solutions" 
                className="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-md transition-colors inline-flex items-center"
              >
                Explore Solutions
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
              <Link 
                href="#categories" 
                className="bg-blue-700 hover:bg-blue-800 text-white font-medium py-3 px-8 rounded-md border border-blue-400 transition-colors"
              >
                Browse Categories
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main HR Apps Section */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Path</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Whether you're looking for your next opportunity or seeking top talent, we have the right platform for you.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* Job Seekers Portal */}
            <Link href="/hr/hunter" className="group">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-200 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="flex items-center mb-6">
                  <div className="p-4 bg-blue-600 rounded-xl">
                    <Search className="h-8 w-8 text-white" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-2xl font-bold text-gray-900">Job Seekers</h3>
                    <p className="text-blue-600 font-medium">Find Your Dream Job</p>
                  </div>
                </div>
                <p className="text-gray-700 mb-6 leading-relaxed">
                  Discover thousands of job opportunities, connect with top companies, and advance your career with our comprehensive job portal.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Job Search & Filters
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Company Profiles
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Application Tracking
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Career Resources
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-600 font-medium">Start Job Search</span>
                  <ArrowRight className="h-5 w-5 text-blue-600 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </Link>

            {/* Headhunters Portal */}
            <Link href="/hr/headhunter" className="group">
              <div className="bg-gradient-to-br from-teal-50 to-teal-100 border-2 border-teal-200 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="flex items-center mb-6">
                  <div className="p-4 bg-teal-600 rounded-xl">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-2xl font-bold text-gray-900">Headhunters</h3>
                    <p className="text-teal-600 font-medium">Recruit Top Talent</p>
                  </div>
                </div>
                <p className="text-gray-700 mb-6 leading-relaxed">
                  Professional recruitment platform with AI-powered tools, global EOR services, and comprehensive talent management solutions.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Candidate Management
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    AI Recruitment Tools
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Global EOR Services
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Business Dashboard
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-teal-600 font-medium">Start Recruiting</span>
                  <ArrowRight className="h-5 w-5 text-teal-600 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>

      {/* Dashboard Stats Section */}
      <div className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">HR Suite Analytics</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Real-time insights across all HR modules and processes
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Total Employees</p>
                  <p className="text-3xl font-bold">{dashboardStats.totalEmployees}</p>
                </div>
                <Users className="h-12 w-12 text-blue-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Active Recruitment</p>
                  <p className="text-3xl font-bold">{dashboardStats.activeRecruitment}</p>
                </div>
                <Search className="h-12 w-12 text-green-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Onboarding in Progress</p>
                  <p className="text-3xl font-bold">{dashboardStats.onboardingInProgress}</p>
                </div>
                <Brain className="h-12 w-12 text-purple-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-indigo-100">Global Hires</p>
                  <p className="text-3xl font-bold">{dashboardStats.globalHires}</p>
                </div>
                <Building2 className="h-12 w-12 text-indigo-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100">Open Disputes</p>
                  <p className="text-3xl font-bold">{dashboardStats.openDisputes}</p>
                </div>
                <Scale className="h-12 w-12 text-yellow-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-teal-100">Completed Projects</p>
                  <p className="text-3xl font-bold">{dashboardStats.completedProjects}</p>
                </div>
                <CheckCircle className="h-12 w-12 text-teal-200" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            {/* Stats Grid */}
            <div className="lg:col-span-2">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                {stats.map((stat, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-6">
                    <div className="flex justify-center mb-3">
                      <stat.icon className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                    <div className="text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* System Status */}
            <div className="lg:col-span-1">
              <HRSuiteStatus />
            </div>
          </div>
        </div>
      </div>

      {/* Categories Section */}
      <div id="categories" className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Solution Categories</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our HR solutions are organized into specialized categories to address every aspect of human resource management.
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            {categories.map((category, index) => (
              <div key={index} className="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <div className="flex justify-center mb-3">
                  <category.icon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-1">{category.name}</h3>
                <p className="text-sm text-gray-500">{category.count} solution{category.count > 1 ? 's' : ''}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* HR Solutions Grid */}
      <div id="solutions" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">HR Solutions</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore our comprehensive suite of HR applications designed to streamline your workforce management processes.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {hrSolutions.map((solution) => {
              const IconComponent = solution.icon;
              return (
                <Link
                  key={solution.id}
                  href={solution.href}
                  className="group"
                >
                  <div className={`border-2 rounded-xl p-6 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105 ${getColorClasses(solution.color)}`}>
                    <div className="flex items-start justify-between mb-4">
                      <div className={`p-3 rounded-lg bg-white shadow-sm ${getIconColorClasses(solution.color)}`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs font-medium text-green-600">{solution.status}</span>
                      </div>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {solution.title}
                    </h3>
                    <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                      {solution.description}
                    </p>

                    <div className="mb-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-gray-700 border">
                        {solution.category}
                      </span>
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-900">Key Features:</h4>
                      <div className="grid grid-cols-2 gap-1">
                        {solution.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center text-xs text-gray-600">
                            <CheckCircle className="h-3 w-3 text-green-500 mr-1 flex-shrink-0" />
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Learn more</span>
                        <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all" />
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </div>

      {/* Integration Benefits */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Integrated HR Ecosystem</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our HR solutions work seamlessly together, providing a unified experience across all your workforce management needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 ml-3">Unified User Management</h3>
              </div>
              <p className="text-gray-600">
                Single sign-on across all HR applications with shared user profiles and permissions management.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 ml-3">Cross-Platform Analytics</h3>
              </div>
              <p className="text-gray-600">
                Comprehensive reporting and analytics that span across all HR solutions for better insights.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Shield className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 ml-3">Enterprise Security</h3>
              </div>
              <p className="text-gray-600">
                Enterprise-grade security and compliance features built into every solution in the suite.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Access */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Quick Access</h2>
            <p className="text-lg text-gray-600">
              Jump directly to the most popular HR solutions
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link
              href="/hr/hunter"
              className="bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-4 text-center transition-colors"
            >
              <Search className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="font-medium text-blue-900">Job Seekers</div>
              <div className="text-xs text-blue-600">Find Jobs</div>
            </Link>

            <Link
              href="/onboarding-buddy"
              className="bg-indigo-50 hover:bg-indigo-100 border border-indigo-200 rounded-lg p-4 text-center transition-colors"
            >
              <Brain className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
              <div className="font-medium text-indigo-900">AI Onboarding</div>
              <div className="text-xs text-indigo-600">New Hires</div>
            </Link>

            <Link
              href="/hr/headhunter"
              className="bg-teal-50 hover:bg-teal-100 border border-teal-200 rounded-lg p-4 text-center transition-colors"
            >
              <Users className="h-8 w-8 text-teal-600 mx-auto mb-2" />
              <div className="font-medium text-teal-900">Headhunters</div>
              <div className="text-xs text-teal-600">Recruit Talent</div>
            </Link>

            <Link
              href="/workdone/hrm"
              className="bg-orange-50 hover:bg-orange-100 border border-orange-200 rounded-lg p-4 text-center transition-colors"
            >
              <UserCheck className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="font-medium text-orange-900">HR Management</div>
              <div className="text-xs text-orange-600">Employee Lifecycle</div>
            </Link>
          </div>
        </div>
      </div>

      {/* Footer CTA */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your HR Operations?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Explore our comprehensive HR suite and discover how our integrated solutions can streamline your workforce management.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/hr/hunter"
              className="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-md transition-colors"
            >
              Job Seekers Portal
            </Link>
            <Link
              href="/hr/headhunter"
              className="bg-blue-700 hover:bg-blue-800 text-white font-medium py-3 px-8 rounded-md border border-blue-400 transition-colors"
            >
              Headhunter Portal
            </Link>
          </div>
        </div>
      </div>
    </div>
    </HRUnifiedLayout>
  );
}
