'use client';

import PaymentsListPage from './payments-list';

export default function PaymentsPage() {
  return <PaymentsListPage />;
}

  // Mock utilities readings
  const utilities = [
    { name: "<PERSON><PERSON><PERSON><PERSON>", unit: "kWh", usage: 120, price: 3500 },
    { name: "<PERSON><PERSON><PERSON><PERSON>", unit: "m³", usage: 8, price: 15000 }
  ];
  const utilitiesTotal = utilities.reduce((sum, u) => sum + u.usage * u.price, 0);

  // Find financial records for this contract and month
  const records = financialRecords.filter((r: any) =>
    r.contractId === contract?.id &&
    r.dueDate?.startsWith(selectedMonth)
  );

  // Get paid and pending records
  const paidRecords = records.filter(r => r.status === "paid" && r.type === "payment");
  const pendingRecords = records.filter(r => r.status !== "paid" || r.type !== "payment");

  // Fetch payments for this contract and month
  useEffect(() => {
    async function fetchPayments() {
      try {
        const res = await fetch("/api/minirent/payments");
        const data = await res.json();
        setPayments(
          (data.payments || []).filter((p: any) =>
            p.contractId === contract?.id &&
            p.date?.startsWith(selectedMonth)
          )
        );
      } catch (error) {
        console.error("Error fetching payments:", error);
        setPayments([]);
      }
    }
    if (contract?.id) fetchPayments();
  }, [selectedProperty, selectedMonth, contract?.id, refreshKey]);

  // Recalculate to include both payment sources
  const totalPendingCharges = pendingRecords.reduce((sum: number, r: any) => sum + r.amount, 0);
  const totalPaid = paidRecords.reduce((sum: number, r: any) => sum + r.amount, 0) + 
                   payments.reduce((sum: number, p: any) => sum + p.amount, 0);
  const totalBill = rent + utilitiesTotal + totalPendingCharges;
  const outstandingAmount = totalBill - totalPaid;

  // Update amount when payment type or outstanding amount changes
  useEffect(() => {
    if (paymentType === 'full' && outstandingAmount > 0) {
      setAmount(outstandingAmount.toString());
    }
  }, [paymentType, outstandingAmount]);

  // Toggle payment type
  const togglePaymentType = (type: 'full' | 'partial') => {
    setPaymentType(type);
    if (type === 'full') {
      setAmount(outstandingAmount.toString());
    } else {
      setAmount('');
    }
  };

  // Auto-hide feedback after 5 seconds
  useEffect(() => {
    if (feedback) {
      const timer = setTimeout(() => {
        setFeedback(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [feedback]);

  // Handle payment save
  async function handleSavePayment(e: React.FormEvent) {
    e.preventDefault();
    if (!amount || !contract?.id) return;
    
    setSubmitting(true);
    setFeedback(null);
    
    const payment = {
      id: `pay-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      contractId: contract.id,
      propertyId: selectedProperty,
      amount: Number(amount),
      method,
      note,
      date: new Date().toISOString(),
    };
    
    try {
      // Try to call both APIs in parallel
      const [paymentRes, financialRes] = await Promise.all([
        // Save to the payments API
        fetch("/api/minirent/payments", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payment),
        }),
        
        // Save to the financial-records API
        fetch("/api/minirent/financial-records", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payment),
        })
      ]);
      
      if (!paymentRes.ok || !financialRes.ok) {
        throw new Error(`API returned an error status`);
      }
      
      await Promise.all([paymentRes.json(), financialRes.json()]);
      
      // Success case
      setFeedback({
        message: "Thanh toán đã được ghi nhận thành công!",
        type: "success"
      });
      
      setAmount("");
      setNote("");
      setMethod(paymentsData.paymentMethods[0]?.id || "");
      setPaymentType('full');
      
      // Add the payment to the local state as fallback
      setPayments(prev => [...prev, payment]);
    } catch (error) {
      console.error("Error saving payment:", error);
      
      // Fallback: Add the payment to the local state
      setPayments(prev => [...prev, payment]);
      
      setFeedback({
        message: "Thanh toán đã được lưu cục bộ (Lỗi kết nối API).",
        type: "success"
      });
      
      setAmount("");
      setNote("");
      setMethod(paymentsData.paymentMethods[0]?.id || "");
      setPaymentType('full');
    } finally {
      setSubmitting(false);
      setRefreshKey(k => k + 1);
    }
  }

  // Handle feedback form input changes
  const handleFeedbackChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFeedbackForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle feedback submission
  const handleFeedbackSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!feedbackForm.description) return;

    setSubmittingFeedback(true);
    
    try {
      // Call the feedback API to save the feedback
      const response = await fetch("/api/minirent/feedback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(feedbackForm),
      });
      
      if (!response.ok) {
        throw new Error(`API returned status ${response.status}`);
      }
      
      const result = await response.json();
      
      // Show success message
      setFeedback({
        message: "Cảm ơn bạn đã gửi phản hồi! Chúng tôi sẽ xem xét nội dung bạn gửi.",
        type: "success"
      });
      
      // Close modal and reset form
      setShowFeedbackModal(false);
      setFeedbackForm({
        type: "error",
        description: "",
        page: "payments",
        email: ""
      });
    } catch (error) {
      console.error("Error submitting feedback:", error);
      setFeedback({
        message: "Có lỗi xảy ra khi gửi phản hồi. Vui lòng thử lại sau.",
        type: "error"
      });
    } finally {
      setSubmittingFeedback(false);
    }
  };

  if (loading) {
    return <div className="max-w-3xl mx-auto p-8 text-center text-gray-500">Đang tải...</div>;
  }

  return (
    <div className="max-w-3xl mx-auto p-8 space-y-8 relative">
      <h1 className="text-3xl font-bold mb-6">Quản Lý Thanh Toán</h1>
      <div className="flex gap-4 mb-4">
        <div>
          <label className="block font-medium mb-1">Bất động sản</label>
          <select
            className="border rounded px-2 py-1"
            value={selectedProperty}
            onChange={e => setSelectedProperty(e.target.value)}
          >
            {properties.map((p: any) => (
              <option key={p.id} value={p.id}>{p.name}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block font-medium mb-1">Tháng</label>
          <select
            className="border rounded px-2 py-1"
            value={selectedMonth}
            onChange={e => setSelectedMonth(e.target.value)}
          >
            {getMonthOptions().map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>
        </div>
      </div>
      <div className="bg-white rounded shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Tổng Hợp Hóa Đơn</h2>
        <div className="mb-2">Người thuê: <b>{tenant?.['ten-khach-thue'] || 'N/A'}</b></div>
        <div className="mb-2">Tiền thuê: <b>{rent.toLocaleString()} VND</b></div>
        <div className="mb-2">Tiện ích:</div>
        <ul className="ml-6 mb-2">
          {utilities.map(u => (
            <li key={u.name}>{u.name}: {u.usage} {u.unit} x {u.price.toLocaleString()} = <b>{(u.usage * u.price).toLocaleString()} VND</b></li>
          ))}
        </ul>
        {pendingRecords.length > 0 && (
          <div className="mb-2">Phí khác:
            <ul className="ml-6">
              {pendingRecords.map((r: any) => (
                <li key={r.id}>
                  {r.type}: <b>{r.amount.toLocaleString()} VND</b> ({r.status})
                  {r.note && <span className="text-gray-500 text-sm ml-2">- {r.note}</span>}
                </li>
              ))}
            </ul>
          </div>
        )}
        <div className="mb-2">Tổng hóa đơn: <b>{totalBill.toLocaleString()} VND</b></div>
        <div className="mb-2">Đã thanh toán: <b>{totalPaid.toLocaleString()} VND</b></div>
        <div className="mb-2">Còn lại: <b>{outstandingAmount.toLocaleString()} VND</b></div>
      </div>
      <div className="bg-white rounded shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Ghi Nhận Thanh Toán</h2>
        
        {feedback && (
          <div className={`p-3 mb-4 rounded ${feedback.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {feedback.message}
          </div>
        )}
        
        <form className="space-y-4" onSubmit={handleSavePayment}>
          <div className="flex space-x-2 mb-4">
            <div className="text-sm font-medium">Loại thanh toán:</div>
            <div className="flex border rounded overflow-hidden">
              <button
                type="button"
                className={`px-4 py-1 text-sm ${paymentType === 'full' ? 'bg-blue-600 text-white' : 'bg-gray-100'}`}
                onClick={() => togglePaymentType('full')}
              >
                Toàn bộ
              </button>
              <button
                type="button"
                className={`px-4 py-1 text-sm ${paymentType === 'partial' ? 'bg-blue-600 text-white' : 'bg-gray-100'}`}
                onClick={() => togglePaymentType('partial')}
              >
                Một phần
              </button>
            </div>
          </div>
          <div>
            <label className="block font-medium mb-1">Số tiền</label>
            <input
              className="border rounded px-2 py-1 w-full"
              type="number"
              value={amount}
              onChange={e => setAmount(e.target.value)}
              min={0}
              max={paymentType === 'partial' ? outstandingAmount : undefined}
              readOnly={paymentType === 'full'}
              required
            />
          </div>
          <div>
            <label className="block font-medium mb-1">Phương thức</label>
            <select
              className="border rounded px-2 py-1 w-full"
              value={method}
              onChange={e => setMethod(e.target.value)}
            >
              {paymentsData.paymentMethods.map((m: any) => (
                <option key={m.id} value={m.id}>{m.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block font-medium mb-1">Ghi chú</label>
            <input
              className="border rounded px-2 py-1 w-full"
              type="text"
              value={note}
              onChange={e => setNote(e.target.value)}
            />
          </div>
          <button
            type="submit"
            className={`px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center justify-center ${submitting ? 'opacity-70 cursor-not-allowed' : ''}`}
            disabled={!amount || submitting}
          >
            {submitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang xử lý...
              </>
            ) : 'Lưu Thanh Toán'}
          </button>
        </form>
      </div>
      <div className="bg-white rounded shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Lịch Sử Thanh Toán</h2>
        {payments.length === 0 && paidRecords.length === 0 ? (
          <div className="text-gray-500">Không có thanh toán nào được ghi nhận trong tháng này.</div>
        ) : (
          <table className="min-w-full border">
            <thead>
              <tr>
                <th className="border px-2 py-1">Ngày</th>
                <th className="border px-2 py-1">Số tiền</th>
                <th className="border px-2 py-1">Phương thức</th>
                <th className="border px-2 py-1">Ghi chú</th>
                <th className="border px-2 py-1">Nguồn</th>
              </tr>
            </thead>
            <tbody>
              {/* Payments from financial records */}
              {paidRecords.map((r: any) => (
                <tr key={r.id}>
                  <td className="border px-2 py-1">{new Date(r.createdAt).toLocaleDateString()}</td>
                  <td className="border px-2 py-1">{r.amount.toLocaleString()} VND</td>
                  <td className="border px-2 py-1">{r.paymentMethod}</td>
                  <td className="border px-2 py-1">{r.note || '-'}</td>
                  <td className="border px-2 py-1">Hồ sơ tài chính</td>
                </tr>
              ))}
              
              {/* Payments from payments API */}
              {payments.map((p: any) => (
                <tr key={p.id}>
                  <td className="border px-2 py-1">{new Date(p.date).toLocaleDateString()}</td>
                  <td className="border px-2 py-1">{p.amount.toLocaleString()} VND</td>
                  <td className="border px-2 py-1">{paymentsData.paymentMethods.find((m: any) => m.id === p.method)?.name || p.method}</td>
                  <td className="border px-2 py-1">{p.note || '-'}</td>
                  <td className="border px-2 py-1">Thanh toán</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Feedback floating button */}
      <button
        onClick={() => setShowFeedbackModal(true)}
        className="fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg flex items-center justify-center transition-all"
        title="Gửi phản hồi"
      >
        <MessageCircle size={24} />
      </button>

      {/* Feedback Modal */}
      {showFeedbackModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md relative">
            <button 
              onClick={() => setShowFeedbackModal(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>
            
            <h3 className="text-xl font-semibold mb-4">Gửi phản hồi</h3>
            
            <form onSubmit={handleFeedbackSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Loại phản hồi</label>
                <select
                  name="type"
                  value={feedbackForm.type}
                  onChange={handleFeedbackChange}
                  className="w-full border rounded-md px-3 py-2"
                >
                  {feedbackTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nội dung phản hồi</label>
                <textarea
                  name="description"
                  value={feedbackForm.description}
                  onChange={handleFeedbackChange}
                  rows={4}
                  className="w-full border rounded-md px-3 py-2"
                  placeholder="Mô tả chi tiết vấn đề bạn gặp phải..."
                  required
                ></textarea>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email liên hệ (không bắt buộc)</label>
                <input
                  type="email"
                  name="email"
                  value={feedbackForm.email}
                  onChange={handleFeedbackChange}
                  className="w-full border rounded-md px-3 py-2"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <button
                type="submit"
                className={`w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 ${submittingFeedback ? 'opacity-70 cursor-not-allowed' : ''}`}
                disabled={submittingFeedback}
              >
                {submittingFeedback ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Đang gửi...
                  </>
                ) : (
                  <>
                    <Send size={18} className="mr-2" />
                    Gửi phản hồi
                  </>
                )}
              </button>
            </form>
          </div>
        </div>
      )}

      {/* Footer with feedback link */}
      <div className="mt-10 pt-6 border-t text-center text-sm text-gray-500">
        <p>
          Gặp vấn đề? <button className="text-blue-600 hover:underline" onClick={() => setShowFeedbackModal(true)}>
            Gửi phản hồi
          </button>
        </p>
      </div>
    </div>
  );
} 